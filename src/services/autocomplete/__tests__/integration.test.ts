import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import * as vscode from "vscode"
import { AutocompleteConfigManager } from "../AutocompleteConfigManager"
import { registerAutocomplete } from "../AutocompleteProvider"
import { DEFAULT_AUTOCOMPLETE_SETTINGS } from "../../../shared/AutocompleteSettings"

// Mock vscode module
vi.mock("vscode", () => ({
	window: {
		createStatusBarItem: vi.fn(() => ({
			text: "",
			tooltip: "",
			command: "",
			show: vi.fn(),
			dispose: vi.fn(),
		})),
		showInformationMessage: vi.fn(),
	},
	workspace: {
		getConfiguration: vi.fn(() => ({
			get: vi.fn((key: string, defaultValue?: any) => {
				const config: Record<string, any> = {
					enabled: false,
					apiBaseUrl: "https://api.openrouter.ai/api/v1",
					modelId: "google/gemini-2.5-flash-preview-05-20",
					maxTokens: 1000,
					temperature: 0.1,
					requestTimeoutMs: 30000,
					usePromptCache: false,
					customHeaders: {},
				}
				return config[key] ?? defaultValue
			}),
			update: vi.fn(),
		})),
	},
	commands: {
		registerCommand: vi.fn(() => ({ dispose: vi.fn() })),
	},
	languages: {
		registerInlineCompletionItemProvider: vi.fn(() => ({ dispose: vi.fn() })),
	},
	StatusBarAlignment: {
		Right: 2,
	},
	ConfigurationTarget: {
		Global: 1,
	},
}))

describe("Autocomplete Integration Tests", () => {
	let mockContext: any

	beforeEach(() => {
		mockContext = {
			globalState: {
				get: vi.fn((key: string) => {
					if (key === "autocompleteApiKey") return undefined
					return undefined
				}),
				update: vi.fn(),
			},
			subscriptions: [],
		}
		vi.clearAllMocks()
	})

	afterEach(() => {
		vi.clearAllMocks()
	})

	describe("AutocompleteConfigManager", () => {
		it("should initialize with default settings", () => {
			AutocompleteConfigManager.initialize(mockContext)
			const manager = AutocompleteConfigManager.instance
			const settings = manager.getSettings()

			expect(settings.enabled).toBe(DEFAULT_AUTOCOMPLETE_SETTINGS.enabled)
			expect(settings.apiBaseUrl).toBe(DEFAULT_AUTOCOMPLETE_SETTINGS.apiBaseUrl)
			expect(settings.modelId).toBe(DEFAULT_AUTOCOMPLETE_SETTINGS.modelId)
		})

		it("should update settings correctly", async () => {
			AutocompleteConfigManager.initialize(mockContext)
			const manager = AutocompleteConfigManager.instance

			const newSettings = {
				enabled: true,
				apiKey: "test-api-key",
				modelId: "test-model",
			}

			await manager.updateSettings(newSettings)

			// Verify that the configuration was updated
			expect(vscode.workspace.getConfiguration).toHaveBeenCalledWith("cline.autocomplete")
		})

		it("should provide compatibility methods", () => {
			AutocompleteConfigManager.initialize(mockContext)
			const manager = AutocompleteConfigManager.instance

			const providerSettings = manager.getProviderSettings()
			expect(providerSettings).toHaveProperty("qaxToken")
			expect(providerSettings).toHaveProperty("qaxModel")
			expect(providerSettings).toHaveProperty("qaxBaseUrl")

			const globalState = manager.getGlobalState("experiments")
			expect(globalState).toHaveProperty("autocomplete")
		})
	})

	describe("registerAutocomplete", () => {
		it("should register autocomplete without errors", () => {
			expect(() => {
				registerAutocomplete(mockContext)
			}).not.toThrow()

			// Verify that commands were registered
			expect(vscode.commands.registerCommand).toHaveBeenCalledWith(
				"qax-code.toggleAutocomplete",
				expect.any(Function)
			)
			expect(vscode.commands.registerCommand).toHaveBeenCalledWith(
				"qax-code.trackAcceptedSuggestion",
				expect.any(Function)
			)
		})

		it("should create status bar item", () => {
			registerAutocomplete(mockContext)

			expect(vscode.window.createStatusBarItem).toHaveBeenCalledWith(
				vscode.StatusBarAlignment.Right,
				100
			)
		})
	})

	describe("Configuration Integration", () => {
		it("should handle configuration changes", async () => {
			AutocompleteConfigManager.initialize(mockContext)
			const manager = AutocompleteConfigManager.instance

			// Test enabling autocomplete
			await manager.updateSettings({ enabled: true, apiKey: "test-key" })
			expect(manager.isEnabled()).toBe(true)
			expect(manager.hasApiKey()).toBe(true)

			// Test disabling autocomplete
			await manager.updateSettings({ enabled: false })
			expect(manager.isEnabled()).toBe(false)
		})
	})
})
