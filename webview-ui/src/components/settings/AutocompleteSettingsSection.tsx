import { useExtensionState } from "@/context/ExtensionStateContext"
import { VSCodeButton, VSCodeCheckbox, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { useState, useEffect } from "react"
import Section from "./Section"
import SectionHeader from "./SectionHeader"
import { <PERSON>pi<PERSON>ey<PERSON>ield } from "./common/ApiKeyField"
import { BaseUrlField } from "./common/BaseUrlField"
import { ErrorMessage } from "./common/ErrorMessage"

interface AutocompleteSettingsSectionProps {
	// No props needed for now
}

export default function AutocompleteSettingsSection(_props: AutocompleteSettingsSectionProps) {
	const { autocompleteSettings, setAutocompleteSettings } = useExtensionState()
	const [localSettings, setLocalSettings] = useState(autocompleteSettings)
	const [errors, setErrors] = useState<string[]>([])

	// Update local settings when global settings change
	useEffect(() => {
		setLocalSettings(autocompleteSettings)
	}, [autocompleteSettings])

	const handleInputChange = (field: keyof typeof localSettings) => (event: any) => {
		const value = event.target?.value ?? event.detail?.value ?? event
		setLocalSettings(prev => ({
			...prev,
			[field]: value
		}))
	}

	const handleCheckboxChange = (field: keyof typeof localSettings) => (event: any) => {
		const checked = event.target?.checked ?? event.detail?.checked ?? event
		setLocalSettings(prev => ({
			...prev,
			[field]: checked
		}))
	}

	const handleSave = async () => {
		// Validate settings
		const validationErrors: string[] = []
		
		if (localSettings.enabled && !localSettings.apiKey) {
			validationErrors.push("API key is required when autocomplete is enabled")
		}

		if (localSettings.apiBaseUrl && !isValidUrl(localSettings.apiBaseUrl)) {
			validationErrors.push("Invalid API base URL")
		}

		if (localSettings.maxTokens && (localSettings.maxTokens < 1 || localSettings.maxTokens > 10000)) {
			validationErrors.push("Max tokens must be between 1 and 10000")
		}

		if (localSettings.temperature && (localSettings.temperature < 0 || localSettings.temperature > 2)) {
			validationErrors.push("Temperature must be between 0 and 2")
		}

		if (localSettings.requestTimeoutMs && (localSettings.requestTimeoutMs < 1000 || localSettings.requestTimeoutMs > 300000)) {
			validationErrors.push("Request timeout must be between 1000ms and 300000ms")
		}

		setErrors(validationErrors)

		if (validationErrors.length === 0) {
			setAutocompleteSettings(localSettings)
		}
	}

	const handleReset = () => {
		setLocalSettings(autocompleteSettings)
		setErrors([])
	}

	const isValidUrl = (url: string): boolean => {
		try {
			new URL(url)
			return true
		} catch {
			return false
		}
	}

	const hasChanges = JSON.stringify(localSettings) !== JSON.stringify(autocompleteSettings)

	return (
		<Section>
			<SectionHeader description="Configure QAX autocomplete settings for intelligent code completion">
				QAX Autocomplete Configuration
			</SectionHeader>
			
			{errors.length > 0 && (
				<div style={{ marginBottom: "16px" }}>
					{errors.map((error, index) => (
						<ErrorMessage key={index} message={error} />
					))}
				</div>
			)}

			<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
				<VSCodeCheckbox
					checked={localSettings.enabled}
					onChange={handleCheckboxChange("enabled")}>
					Enable QAX Autocomplete
				</VSCodeCheckbox>

				<ApiKeyField
					value={localSettings.apiKey || ""}
					onChange={handleInputChange("apiKey")}
					providerName="QAX Autocomplete"
					placeholder="Enter your API key..."
				/>

				<BaseUrlField
					value={localSettings.apiBaseUrl || ""}
					onChange={handleInputChange("apiBaseUrl")}
					placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
					label="API Base URL"
				/>

				<VSCodeTextField
					value={localSettings.modelId || ""}
					style={{ width: "100%" }}
					onInput={handleInputChange("modelId")}
					placeholder="google/gemini-2.5-flash-preview-05-20">
					<span style={{ fontWeight: 500 }}>Model ID</span>
				</VSCodeTextField>

				<VSCodeTextField
					value={localSettings.maxTokens?.toString() || ""}
					style={{ width: "100%" }}
					onInput={(e) => handleInputChange("maxTokens")(parseInt(e.target.value) || 1000)}
					placeholder="1000">
					<span style={{ fontWeight: 500 }}>Max Tokens</span>
				</VSCodeTextField>

				<VSCodeTextField
					value={localSettings.temperature?.toString() || ""}
					style={{ width: "100%" }}
					onInput={(e) => handleInputChange("temperature")(parseFloat(e.target.value) || 0.1)}
					placeholder="0.1">
					<span style={{ fontWeight: 500 }}>Temperature</span>
				</VSCodeTextField>

				<VSCodeTextField
					value={localSettings.requestTimeoutMs?.toString() || ""}
					style={{ width: "100%" }}
					onInput={(e) => handleInputChange("requestTimeoutMs")(parseInt(e.target.value) || 30000)}
					placeholder="30000">
					<span style={{ fontWeight: 500 }}>Request Timeout (ms)</span>
				</VSCodeTextField>

				<VSCodeCheckbox
					checked={localSettings.usePromptCache || false}
					onChange={handleCheckboxChange("usePromptCache")}>
					Use Prompt Cache
				</VSCodeCheckbox>

				{hasChanges && (
					<div style={{ display: "flex", gap: "8px", marginTop: "16px" }}>
						<VSCodeButton onClick={handleSave}>
							Save Settings
						</VSCodeButton>
						<VSCodeButton appearance="secondary" onClick={handleReset}>
							Reset
						</VSCodeButton>
					</div>
				)}
			</div>

			<div style={{ 
				marginTop: "24px", 
				padding: "12px", 
				backgroundColor: "var(--vscode-textBlockQuote-background)",
				border: "1px solid var(--vscode-textBlockQuote-border)",
				borderRadius: "4px"
			}}>
				<p style={{
					margin: 0,
					fontSize: "12px",
					color: "var(--vscode-descriptionForeground)"
				}}>
					<strong>Note:</strong> QAX Autocomplete provides intelligent code completion using AI models via OpenAI-compatible APIs.
					Supports OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM Studio), and other compatible services.
					Configure your API key and base URL to enable intelligent code suggestions based on your current context.
				</p>
			</div>
		</Section>
	)
}
